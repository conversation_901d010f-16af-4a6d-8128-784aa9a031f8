import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../core/services/auth_service.dart';
import '../core/services/notification_service.dart';
import '../core/errors/exceptions.dart';
import '../models/user_model.dart';

/// Provider para gerenciamento de autenticação
class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  final NotificationService _notificationService = NotificationService();

  UserModel? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  /// Usuário atual
  UserModel? get currentUser => _currentUser;

  /// Estado de carregamento
  bool get isLoading => _isLoading;

  /// Mensagem de erro
  String? get errorMessage => _errorMessage;

  /// Verifica se o usuário está logado
  bool get isLoggedIn => _currentUser != null;

  /// Inicializa o provider
  Future<void> initialize() async {
    _setLoading(true);

    try {
      // Escutar mudanças no estado de autenticação
      _authService.authStateChanges.listen(_onAuthStateChanged);

      // Verificar se já existe um usuário logado
      final user = _authService.currentUser;
      if (user != null) {
        await _loadCurrentUser();
      }

      // Inicializar notificações
      await _notificationService.initialize();
    } catch (e) {
      _setError('Erro ao inicializar autenticação: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Callback para mudanças no estado de autenticação
  void _onAuthStateChanged(User? user) async {
    if (user != null) {
      await _loadCurrentUser();
    } else {
      _currentUser = null;
      notifyListeners();
    }
  }

  /// Carrega os dados do usuário atual
  Future<void> _loadCurrentUser() async {
    try {
      final userModel = await _authService.getCurrentUserModel();
      _currentUser = userModel;
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar dados do usuário: ${e.toString()}');
    }
  }

  /// Login com Google
  Future<bool> signInWithGoogle() async {
    _setLoading(true);
    _clearError();

    try {
      final userModel = await _authService.signInWithGoogle();
      _currentUser = userModel;

      // Subscrever a notificações gerais do usuário
      await _notificationService.subscribeToTopic('user_${userModel.id}');

      notifyListeners();
      return true;
    } on AuthException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erro inesperado durante o login: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Logout
  Future<bool> signOut() async {
    _setLoading(true);
    _clearError();

    try {
      // Remover subscrições de notificação
      if (_currentUser != null) {
        await _notificationService.unsubscribeFromTopic(
          'user_${_currentUser!.id}',
        );
      }

      await _authService.signOut();
      _currentUser = null;

      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erro ao fazer logout: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Atualiza o perfil do usuário
  Future<bool> updateProfile({String? displayName, String? photoURL}) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _clearError();

    try {
      await _authService.updateProfile(
        displayName: displayName,
        photoURL: photoURL,
      );

      // Recarregar dados do usuário
      await _loadCurrentUser();

      return true;
    } catch (e) {
      _setError('Erro ao atualizar perfil: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Reautentica o usuário
  Future<bool> reauthenticate() async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.reauthenticate();
      return true;
    } catch (e) {
      _setError('Erro na reautenticação: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Deleta a conta do usuário
  Future<bool> deleteAccount() async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _clearError();

    try {
      // Remover subscrições de notificação
      await _notificationService.unsubscribeFromTopic(
        'user_${_currentUser!.id}',
      );

      await _authService.deleteAccount();
      _currentUser = null;

      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erro ao deletar conta: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Recarrega os dados do usuário atual
  Future<void> refreshUser() async {
    if (_authService.currentUser != null) {
      await _loadCurrentUser();
    }
  }

  /// Verifica se as notificações estão habilitadas
  Future<bool> areNotificationsEnabled() async {
    return await _notificationService.areNotificationsEnabled();
  }

  /// Solicita permissão para notificações
  Future<bool> requestNotificationPermission() async {
    return await _notificationService.requestPermission();
  }

  /// Subscreve a notificações de um evento
  Future<void> subscribeToEvent(String eventId) async {
    await _notificationService.subscribeToEvent(eventId);
  }

  /// Remove subscrição de notificações de um evento
  Future<void> unsubscribeFromEvent(String eventId) async {
    await _notificationService.unsubscribeFromEvent(eventId);
  }

  /// Define estado de carregamento
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Define mensagem de erro
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// Limpa mensagem de erro
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Limpa erro manualmente (para UI)
  void clearError() {
    _clearError();
  }
}
