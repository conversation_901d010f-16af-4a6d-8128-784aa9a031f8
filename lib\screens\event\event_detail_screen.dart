import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/utils/extensions.dart';
import '../../providers/auth_provider.dart';
import '../../providers/event_provider.dart';
import '../../models/event_model.dart';
import '../../widgets/common/loading_widget.dart';

/// Tela de detalhes do evento
class EventDetailScreen extends StatefulWidget {
  final String eventId;

  const EventDetailScreen({
    super.key,
    required this.eventId,
  });

  @override
  State<EventDetailScreen> createState() => _EventDetailScreenState();
}

class _EventDetailScreenState extends State<EventDetailScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<EventProvider>().loadEventDetails(widget.eventId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// Constrói a AppBar
  PreferredSizeWidget _buildAppBar() {
    return Consumer<EventProvider>(
      builder: (context, eventProvider, child) {
        final event = eventProvider.currentEvent;
        return AppBar(
          title: Text(event?.name ?? 'Detalhes do Evento'),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.onPrimary,
          elevation: 0,
          actions: [
            if (event != null) _buildMenuButton(event),
          ],
        );
      },
    );
  }

  /// Botão de menu
  Widget _buildMenuButton(EventModel event) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUser = authProvider.currentUser;
        if (currentUser == null) return const SizedBox.shrink();

        final eventProvider = context.read<EventProvider>();
        final isManager = eventProvider.isUserManager(currentUser.id, event);

        return PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, event),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'share',
              child: ListTile(
                leading: Icon(Icons.share),
                title: Text('Compartilhar TAG'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            if (isManager) ...[
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('Editar Evento'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, color: AppColors.error),
                  title: Text('Excluir Evento', style: TextStyle(color: AppColors.error)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  /// Constrói o corpo da tela
  Widget _buildBody() {
    return Consumer<EventProvider>(
      builder: (context, eventProvider, child) {
        if (eventProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Carregando evento...'),
          );
        }

        final event = eventProvider.currentEvent;
        if (event == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppColors.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Evento não encontrado',
                  style: context.textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'Verifique se o evento ainda existe',
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: AppColors.onBackground.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildEventHeader(event),
              const SizedBox(height: 24),
              _buildEventInfo(event),
              const SizedBox(height: 24),
              _buildRequirementsSection(event),
              const SizedBox(height: 24),
              _buildParticipantsSection(event),
              const SizedBox(height: 24),
              _buildActionsSection(event),
            ],
          ),
        );
      },
    );
  }

  /// Cabeçalho do evento
  Widget _buildEventHeader(EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    event.name,
                    style: context.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(event.status),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getStatusText(event.status),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              event.description,
              style: context.textTheme.bodyLarge,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.location_on, size: 16, color: AppColors.primary),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    event.location,
                    style: context.textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.tag, size: 16, color: AppColors.primary),
                const SizedBox(width: 4),
                Text(
                  'TAG: ${event.eventTag}',
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _copyToClipboard(event.eventTag),
                  icon: const Icon(Icons.copy, size: 16),
                  tooltip: 'Copiar TAG',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Informações do evento
  Widget _buildEventInfo(EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informações',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Criado em', _formatDate(event.createdAt)),
            _buildInfoRow('Última atualização', _formatDate(event.updatedAt)),
            _buildInfoRow('Gerenciadores', '${event.managers.length}'),
            _buildInfoRow('Voluntários', '${event.volunteers.length}'),
          ],
        ),
      ),
    );
  }

  /// Linha de informação
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: context.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// Seção de requisitos
  Widget _buildRequirementsSection(EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Requisitos',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            if (event.requiredSkills.isNotEmpty) ...[
              Text(
                'Habilidades Necessárias:',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: event.requiredSkills.map((skill) {
                  return Chip(
                    label: Text(skill),
                    backgroundColor: AppColors.secondaryContainer,
                    labelStyle: const TextStyle(
                      color: AppColors.onSecondaryContainer,
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
            ],
            if (event.requiredResources.isNotEmpty) ...[
              Text(
                'Recursos Necessários:',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: event.requiredResources.map((resource) {
                  return Chip(
                    label: Text(resource),
                    backgroundColor: AppColors.primaryContainer,
                    labelStyle: const TextStyle(
                      color: AppColors.onPrimaryContainer,
                    ),
                  );
                }).toList(),
              ),
            ],
            if (event.requiredSkills.isEmpty && event.requiredResources.isEmpty)
              Text(
                'Nenhum requisito específico',
                style: context.textTheme.bodyMedium?.copyWith(
                  color: AppColors.onBackground.withOpacity(0.7),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Seção de participantes
  Widget _buildParticipantsSection(EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Participantes',
                  style: context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const Spacer(),
                Text(
                  '${event.volunteers.length} voluntários',
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: AppColors.onBackground.withOpacity(0.7),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (event.volunteers.isEmpty)
              Text(
                'Nenhum voluntário cadastrado ainda',
                style: context.textTheme.bodyMedium?.copyWith(
                  color: AppColors.onBackground.withOpacity(0.7),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: event.volunteers.length > 3 ? 3 : event.volunteers.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final volunteer = event.volunteers[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.primaryContainer,
                      child: Text(
                        volunteer.userId.substring(0, 1).toUpperCase(),
                        style: const TextStyle(
                          color: AppColors.onPrimaryContainer,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text('Voluntário ${index + 1}'),
                    subtitle: Text(
                      'Habilidades: ${volunteer.skills.join(', ')}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getVolunteerStatusColor(volunteer.status),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getVolunteerStatusText(volunteer.status),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
            if (event.volunteers.length > 3) ...[
              const SizedBox(height: 8),
              TextButton(
                onPressed: () => _navigateToVolunteers(event),
                child: Text('Ver todos os ${event.volunteers.length} voluntários'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Seção de ações
  Widget _buildActionsSection(EventModel event) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUser = authProvider.currentUser;
        if (currentUser == null) return const SizedBox.shrink();

        final eventProvider = context.read<EventProvider>();
        final isManager = eventProvider.isUserManager(currentUser.id, event);
        final isVolunteer = eventProvider.isUserVolunteer(currentUser.id, event);

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Ações',
                  style: context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 16),
                if (isManager) ...[
                  _buildActionButton(
                    icon: Icons.task,
                    label: 'Gerenciar Tarefas',
                    onPressed: () => _navigateToTasks(event),
                  ),
                  const SizedBox(height: 8),
                  _buildActionButton(
                    icon: Icons.people,
                    label: 'Gerenciar Voluntários',
                    onPressed: () => _navigateToVolunteers(event),
                  ),
                  const SizedBox(height: 8),
                  _buildActionButton(
                    icon: Icons.analytics,
                    label: 'Acompanhar Progresso',
                    onPressed: () => _navigateToProgress(event),
                  ),
                ] else if (isVolunteer) ...[
                  _buildActionButton(
                    icon: Icons.assignment,
                    label: 'Minhas Tarefas',
                    onPressed: () => _navigateToMyTasks(event),
                  ),
                ] else ...[
                  _buildActionButton(
                    icon: Icons.person_add,
                    label: 'Entrar como Voluntário',
                    onPressed: () => _joinAsVolunteer(event),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  /// Botão de ação
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(label),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.onPrimary,
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }

  /// Botão flutuante
  Widget? _buildFloatingActionButton() {
    return Consumer2<AuthProvider, EventProvider>(
      builder: (context, authProvider, eventProvider, child) {
        final currentUser = authProvider.currentUser;
        final event = eventProvider.currentEvent;
        
        if (currentUser == null || event == null) return null;
        
        final isManager = eventProvider.isUserManager(currentUser.id, event);
        
        if (!isManager) return null;

        return FloatingActionButton(
          onPressed: () => _navigateToTasks(event),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.onPrimary,
          child: const Icon(Icons.add_task),
        );
      },
    );
  }

  /// Obtém cor do status
  Color _getStatusColor(EventStatus status) {
    switch (status) {
      case EventStatus.active:
        return AppColors.success;
      case EventStatus.completed:
        return AppColors.info;
      case EventStatus.cancelled:
        return AppColors.error;
    }
  }

  /// Obtém texto do status
  String _getStatusText(EventStatus status) {
    switch (status) {
      case EventStatus.active:
        return 'ATIVO';
      case EventStatus.completed:
        return 'CONCLUÍDO';
      case EventStatus.cancelled:
        return 'CANCELADO';
    }
  }

  /// Obtém cor do status do voluntário
  Color _getVolunteerStatusColor(VolunteerStatus status) {
    switch (status) {
      case VolunteerStatus.active:
        return AppColors.success;
      case VolunteerStatus.inactive:
        return AppColors.warning;
    }
  }

  /// Obtém texto do status do voluntário
  String _getVolunteerStatusText(VolunteerStatus status) {
    switch (status) {
      case VolunteerStatus.active:
        return 'ATIVO';
      case VolunteerStatus.inactive:
        return 'INATIVO';
    }
  }

  /// Formata data
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Copia para clipboard
  void _copyToClipboard(String text) {
    // TODO: Implementar cópia para clipboard
    context.showSnackBar('TAG copiada: $text');
  }

  /// Manipula ações do menu
  void _handleMenuAction(String action, EventModel event) {
    switch (action) {
      case 'share':
        _shareEvent(event);
        break;
      case 'edit':
        _editEvent(event);
        break;
      case 'delete':
        _deleteEvent(event);
        break;
    }
  }

  /// Compartilha evento
  void _shareEvent(EventModel event) {
    // TODO: Implementar compartilhamento
    context.showSnackBar('Compartilhar: ${event.eventTag}');
  }

  /// Edita evento
  void _editEvent(EventModel event) {
    // TODO: Navegar para tela de edição
    context.showSnackBar('Editar evento em desenvolvimento');
  }

  /// Deleta evento
  Future<void> _deleteEvent(EventModel event) async {
    final confirmed = await context.showConfirmDialog(
      title: 'Confirmar Exclusão',
      content: 'Tem certeza que deseja excluir este evento? Esta ação não pode ser desfeita.',
    );

    if (confirmed == true && mounted) {
      final eventProvider = context.read<EventProvider>();
      final success = await eventProvider.deleteEvent(event.id);
      
      if (success && mounted) {
        context.showSnackBar('Evento excluído com sucesso');
        Navigator.of(context).pop();
      } else if (mounted && eventProvider.errorMessage != null) {
        context.showSnackBar(eventProvider.errorMessage!);
      }
    }
  }

  /// Navega para tarefas
  void _navigateToTasks(EventModel event) {
    // TODO: Implementar navegação para tarefas
    context.showSnackBar('Gerenciar tarefas em desenvolvimento');
  }

  /// Navega para voluntários
  void _navigateToVolunteers(EventModel event) {
    // TODO: Implementar navegação para voluntários
    context.showSnackBar('Gerenciar voluntários em desenvolvimento');
  }

  /// Navega para progresso
  void _navigateToProgress(EventModel event) {
    // TODO: Implementar navegação para progresso
    context.showSnackBar('Acompanhar progresso em desenvolvimento');
  }

  /// Navega para minhas tarefas
  void _navigateToMyTasks(EventModel event) {
    // TODO: Implementar navegação para minhas tarefas
    context.showSnackBar('Minhas tarefas em desenvolvimento');
  }

  /// Entrar como voluntário
  void _joinAsVolunteer(EventModel event) {
    // TODO: Implementar entrada como voluntário
    context.showSnackBar('Entrar como voluntário em desenvolvimento');
  }
}
