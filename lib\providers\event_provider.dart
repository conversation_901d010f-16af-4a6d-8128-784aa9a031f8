import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../core/services/firestore_service.dart';
import '../core/services/notification_service.dart';
import '../core/errors/exceptions.dart';
import '../models/event_model.dart';
import '../models/volunteer_model.dart';
import '../models/user_model.dart';

/// Provider para gerenciamento de eventos
class EventProvider extends ChangeNotifier {
  final FirestoreService _firestoreService = FirestoreService();
  final NotificationService _notificationService = NotificationService();
  final Uuid _uuid = const Uuid();

  List<EventModel> _userEvents = [];
  EventModel? _currentEvent;
  bool _isLoading = false;
  String? _errorMessage;

  /// Lista de eventos do usuário
  List<EventModel> get userEvents => _userEvents;

  /// Evento atual selecionado
  EventModel? get currentEvent => _currentEvent;

  /// Estado de carregamento
  bool get isLoading => _isLoading;

  /// Mensagem de erro
  String? get errorMessage => _errorMessage;

  /// Carrega eventos do usuário
  Future<void> loadUserEvents(String userId) async {
    _setLoading(true);
    _clearError();

    try {
      _userEvents = await _firestoreService.getUserEvents(userId);
      notifyListeners();
    } on DataException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('Erro ao carregar eventos: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Cria um novo evento
  Future<bool> createEvent({
    required String name,
    required String description,
    required String location,
    required String createdBy,
    required List<String> requiredSkills,
    required List<String> requiredResources,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final eventId = _uuid.v4();
      final eventTag = _generateEventTag();

      final event = EventModel.create(
        id: eventId,
        name: name,
        description: description,
        location: location,
        eventTag: eventTag,
        createdBy: createdBy,
        requiredSkills: requiredSkills,
        requiredResources: requiredResources,
      );

      await _firestoreService.createEvent(event);

      // Adicionar à lista local
      _userEvents.add(event);

      // Subscrever criador às notificações do evento
      await _notificationService.subscribeToEvent(eventId);

      notifyListeners();
      return true;
    } on DataException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erro ao criar evento: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Busca evento por TAG
  Future<EventModel?> findEventByTag(String eventTag) async {
    _setLoading(true);
    _clearError();

    try {
      final event = await _firestoreService.getEventByTag(eventTag);
      return event;
    } on DataException catch (e) {
      _setError(e.message);
      return null;
    } catch (e) {
      _setError('Erro ao buscar evento: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Adiciona voluntário ao evento
  Future<bool> joinEvent({
    required String eventId,
    required UserModel user,
    required List<String> availableDays,
    required AvailableHours availableHours,
    required List<String> skills,
    required List<String> resources,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final volunteer = VolunteerModel.create(
        userId: user.id,
        availableDays: availableDays,
        availableHours: availableHours,
        skills: skills,
        resources: resources,
      );

      await _firestoreService.addVolunteerToEvent(eventId, volunteer);

      // Subscrever voluntário às notificações do evento
      await _notificationService.subscribeToEvent(eventId);

      // Recarregar eventos do usuário
      await loadUserEvents(user.id);

      return true;
    } on DataException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erro ao entrar no evento: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Carrega detalhes de um evento específico
  Future<void> loadEventDetails(String eventId) async {
    _setLoading(true);
    _clearError();

    try {
      _currentEvent = await _firestoreService.getEvent(eventId);
      notifyListeners();
    } on DataException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('Erro ao carregar detalhes do evento: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Atualiza evento
  Future<bool> updateEvent(EventModel event) async {
    _setLoading(true);
    _clearError();

    try {
      final updatedEvent = event.updated();
      await _firestoreService.updateEvent(updatedEvent);

      // Atualizar na lista local
      final index = _userEvents.indexWhere((e) => e.id == event.id);
      if (index != -1) {
        _userEvents[index] = updatedEvent;
      }

      // Atualizar evento atual se for o mesmo
      if (_currentEvent?.id == event.id) {
        _currentEvent = updatedEvent;
      }

      notifyListeners();
      return true;
    } on DataException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erro ao atualizar evento: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Deleta evento
  Future<bool> deleteEvent(String eventId) async {
    _setLoading(true);
    _clearError();

    try {
      await _firestoreService.deleteEvent(eventId);

      // Remover da lista local
      _userEvents.removeWhere((e) => e.id == eventId);

      // Limpar evento atual se for o mesmo
      if (_currentEvent?.id == eventId) {
        _currentEvent = null;
      }

      // Remover subscrição de notificações
      await _notificationService.unsubscribeFromEvent(eventId);

      notifyListeners();
      return true;
    } on DataException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erro ao deletar evento: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Verifica se usuário é gerenciador do evento
  bool isUserManager(String userId, EventModel event) {
    return event.managers.contains(userId);
  }

  /// Verifica se usuário é voluntário do evento
  bool isUserVolunteer(String userId, EventModel event) {
    return event.volunteers.any((v) => v.userId == userId);
  }

  /// Gera TAG única para evento
  String _generateEventTag() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    var tag = '';
    
    for (int i = 0; i < 6; i++) {
      tag += chars[(random + i) % chars.length];
    }
    
    return tag;
  }

  /// Limpa evento atual
  void clearCurrentEvent() {
    _currentEvent = null;
    notifyListeners();
  }

  /// Define estado de carregamento
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Define mensagem de erro
  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// Limpa mensagem de erro
  void _clearError() {
    _errorMessage = null;
  }

  /// Limpa todos os dados
  void clear() {
    _userEvents.clear();
    _currentEvent = null;
    _clearError();
    notifyListeners();
  }
}
