# Event Task Manager - Especificação do Projeto

## 📋 Visão Geral
Aplicativo Flutter para gerenciamento colaborativo de tarefas em eventos, onde um organizador pode criar eventos, definir tarefas e gerenciar voluntários para executá-las.

## 🎯 Objetivo
Facilitar a organização e distribuição de tarefas em eventos, permitindo que organizadores criem eventos e voluntários se candidatem para executar microtarefas específicas.

## 🛠️ Stack Tecnológica
- **Frontend**: Flutter (Dart)
- **Backend**: Firebase
  - Authentication (Google Sign-In)
  - Firestore Database
  - Cloud Functions (se necessário)
- **Gerenciamento de Estado**: Provider ou BLoC
- **Roteamento**: GoRouter
- **UI Components**: Material 3

## 📁 Estrutura de Pastas

```
lib/
├── core/
│   ├── constants/
│   │   ├── app_colors.dart
│   │   ├── app_strings.dart
│   │   └── app_routes.dart
│   ├── services/
│   │   ├── auth_service.dart
│   │   ├── firestore_service.dart
│   │   └── notification_service.dart
│   ├── utils/
│   │   ├── validators.dart
│   │   ├── helpers.dart
│   │   └── extensions.dart
│   └── errors/
│       └── exceptions.dart
├── models/
│   ├── user_model.dart
│   ├── event_model.dart
│   ├── task_model.dart
│   ├── microtask_model.dart
│   └── volunteer_model.dart
├── providers/
│   ├── auth_provider.dart
│   ├── event_provider.dart
│   ├── task_provider.dart
│   └── volunteer_provider.dart
├── screens/
│   ├── auth/
│   │   ├── login_screen.dart
│   │   └── register_screen.dart
│   ├── home/
│   │   └── home_screen.dart
│   ├── event/
│   │   ├── create_event_screen.dart
│   │   ├── join_event_screen.dart
│   │   ├── event_detail_screen.dart
│   │   ├── event_tasks_screen.dart
│   │   ├── manage_volunteers_screen.dart
│   │   └── track_tasks_screen.dart
│   └── profile/
│       └── profile_screen.dart
├── widgets/
│   ├── common/
│   │   ├── custom_app_bar.dart
│   │   ├── custom_button.dart
│   │   ├── custom_text_field.dart
│   │   └── loading_widget.dart
│   ├── event/
│   │   ├── event_card.dart
│   │   ├── task_card.dart
│   │   └── volunteer_card.dart
│   └── forms/
│       ├── event_form.dart
│       └── volunteer_form.dart
└── main.dart
```

## 🗃️ Estrutura do Banco de Dados (Firestore)

### Collection: users
```json
{
  "id": "user_id",
  "name": "Nome do Usuário",
  "email": "<EMAIL>",
  "photoUrl": "url_da_foto",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### Collection: events
```json
{
  "id": "event_id",
  "name": "Nome do Evento",
  "description": "Descrição detalhada",
  "location": "Endereço/Local descritivo",
  "eventTag": "TAG123", // Código único para entrada
  "createdBy": "user_id",
  "createdAt": "timestamp",
  "updatedAt": "timestamp",
  "requiredSkills": ["skill1", "skill2"],
  "requiredResources": ["resource1", "resource2"],
  "managers": ["user_id1", "user_id2"],
  "volunteers": [
    {
      "userId": "user_id",
      "joinedAt": "timestamp",
      "availableDays": ["monday", "tuesday"],
      "availableHours": {
        "start": "09:00",
        "end": "17:00"
      },
      "skills": ["skill1"],
      "resources": ["resource1"],
      "status": "active" // active, inactive
    }
  ],
  "status": "active" // active, completed, cancelled
}
```

### Collection: tasks
```json
{
  "id": "task_id",
  "eventId": "event_id",
  "title": "Título da Tarefa",
  "description": "Descrição da tarefa",
  "priority": "high", // high, medium, low
  "estimatedDuration": 120, // em minutos
  "requiredSkills": ["skill1"],
  "requiredResources": ["resource1"],
  "createdBy": "user_id",
  "createdAt": "timestamp",
  "updatedAt": "timestamp",
  "status": "pending", // pending, in_progress, completed
  "dueDate": "timestamp"
}
```

### Collection: microtasks
```json
{
  "id": "microtask_id",
  "taskId": "task_id",
  "eventId": "event_id",
  "title": "Título da Microtarefa",
  "description": "Descrição detalhada",
  "assignedTo": "user_id", // null se não atribuída
  "assignedAt": "timestamp",
  "assignedBy": "user_id", // gerenciador que atribuiu
  "estimatedDuration": 30, // em minutos
  "priority": "medium",
  "requiredSkills": ["skill1"],
  "requiredResources": ["resource1"],
  "status": "pending", // pending, assigned, in_progress, completed
  "createdAt": "timestamp",
  "updatedAt": "timestamp",
  "completedAt": "timestamp"
}
```

## 🔐 Fluxo de Autenticação
1. Login com Google (Firebase Auth)
   - **Web**: `signInWithPopup` com GoogleAuthProvider
   - **Mobile/Desktop**: GoogleSignIn tradicional
   - Detecção automática de plataforma com `kIsWeb`
2. Criação automática do perfil do usuário no Firestore
3. Gerenciamento de estado com Provider
4. Suporte a scopes customizados e parâmetros adicionais

## 📱 Fluxo de Navegação

### Telas Principais:
1. **Login/Cadastro** → Autenticação
2. **Home** → Lista de eventos vinculados
3. **Criar Evento** → Formulário de criação
4. **Entrar em Evento** → Input da TAG do evento
5. **Detalhes do Evento** → Informações gerais
6. **Gerenciar Tarefas** → CRUD de tasks/microtasks
7. **Gerenciar Voluntários** → Atribuição e gerenciamento
8. **Acompanhar Progresso** → Status das tarefas

### Navegação por Roles:
- **Gerenciador**: Acesso completo a todas as funcionalidades
- **Voluntário**: Visualização de tarefas atribuídas e candidatura

## 🎨 Funcionalidades Detalhadas

### Para Gerenciadores:
- ✅ Criar eventos com TAG única
- ✅ Definir habilidades e recursos necessários
- ✅ Criar e editar tasks/microtasks
- ✅ Visualizar voluntários candidatos
- ✅ Atribuir microtasks manualmente
- ⏳ Atribuir microtasks automaticamente (algoritmo heurístico) - *Em desenvolvimento*
- ✅ Promover voluntários a gerenciadores
- ✅ Acompanhar progresso das tarefas

### Para Voluntários:
- ✅ Entrar em eventos via TAG
- ✅ Definir disponibilidade (dias/horários)
- ✅ Selecionar habilidades e recursos disponíveis
- ✅ Visualizar microtasks atribuídas
- ✅ Atualizar status das microtasks
- ✅ Receber notificações de novas atribuições

## 🤖 Algoritmo Heurístico de Atribuição
O algoritmo deve considerar:
1. **Compatibilidade de habilidades** (peso: 40%)
2. **Disponibilidade de recursos** (peso: 30%)
3. **Disponibilidade de horário** (peso: 20%)
4. **Carga de trabalho atual** (peso: 10%)

```dart
double calculateVolunteerScore(Volunteer volunteer, Microtask microtask) {
  double skillScore = calculateSkillMatch(volunteer.skills, microtask.requiredSkills);
  double resourceScore = calculateResourceMatch(volunteer.resources, microtask.requiredResources);
  double timeScore = calculateTimeAvailability(volunteer.availableHours, microtask.estimatedDuration);
  double workloadScore = calculateCurrentWorkload(volunteer.assignedMicrotasks);
  
  return (skillScore * 0.4) + (resourceScore * 0.3) + (timeScore * 0.2) + (workloadScore * 0.1);
}
```

## 🔄 Estados e Transições

### Estados do Evento:
- `active` → `completed` → `archived`
- `active` → `cancelled`

### Estados da Tarefa:
- `pending` → `in_progress` → `completed`

### Estados da Microtarefa:
- `pending` → `assigned` → `in_progress` → `completed`

## 📊 Métricas e Relatórios
- Total de eventos criados/participados
- Taxa de conclusão de tarefas
- Tempo médio de execução
- Ranking de voluntários mais ativos

## 🚀 Funcionalidades Futuras (V2)
- Chat interno por evento
- Sistema de gamificação
- Notificações push
- Relatórios avançados
- Integração com calendário
- Geolocalização para eventos presenciais

## 📋 Checklist de Implementação
- [x] Configurar Firebase e autenticação
  - [x] Firebase Core configurado
  - [x] Firebase Auth implementado
  - [x] Google Sign-In para web e mobile
  - [x] AuthService com signInWithPopup para web
  - [x] AuthProvider para gerenciamento de estado
- [x] Implementar modelos de dados
  - [x] UserModel
  - [x] EventModel
  - [x] TaskModel
  - [x] MicrotaskModel
  - [x] VolunteerModel
- [x] Criar telas de autenticação
  - [x] LoginScreen implementada
  - [x] Integração com AuthProvider
  - [x] Navegação baseada em estado de auth
- [x] Implementar CRUD de eventos
  - [x] FirestoreService para eventos
  - [x] Operações básicas de CRUD
  - [x] HomeScreen para listagem
- [x] Desenvolver sistema de voluntariado
  - [x] Modelo de voluntário
  - [x] Integração com eventos
  - [x] Sistema de habilidades e recursos
- [x] Criar gerenciamento de tarefas
  - [x] Modelos de Task e Microtask
  - [x] CRUD no FirestoreService
  - [x] Sistema de atribuição
- [ ] Implementar algoritmo de atribuição
  - [ ] Algoritmo heurístico
  - [ ] Cálculo de compatibilidade
  - [ ] Atribuição automática
- [x] Adicionar notificações
  - [x] NotificationService implementado
  - [x] Firebase Messaging configurado
  - [x] Subscrição a tópicos
- [x] Testes unitários e de integração
  - [x] Estrutura de testes criada
  - [x] Testes básicos do AuthService
  - [x] Análise estática configurada
- [ ] Deploy e distribuição
  - [ ] Build para produção
  - [ ] Configuração de domínios
  - [ ] Deploy web/mobile

---

## 📊 Status Atual do Projeto

### ✅ **Implementado e Funcionando:**
- Sistema de autenticação completo (web + mobile)
- Modelos de dados estruturados
- Serviços Firebase (Auth, Firestore, Messaging)
- Telas básicas de autenticação e home
- Gerenciamento de estado com Provider
- Estrutura de testes unitários

### ⏳ **Em Desenvolvimento:**
- Algoritmo heurístico de atribuição automática
- Telas completas de gerenciamento de eventos
- Interface de voluntariado avançada

### 📋 **Próximas Prioridades:**
1. Finalizar algoritmo de atribuição automática
2. Implementar telas de criação/edição de eventos
3. Desenvolver interface de gerenciamento de tarefas
4. Adicionar telas de voluntariado
5. Testes de integração completos
6. Deploy para produção

---

## 🎯 Próximos Passos Técnicos
1. ✅ ~~Revisar e aprovar esta especificação~~
2. ✅ ~~Configurar ambiente de desenvolvimento~~
3. ✅ ~~Implementar autenticação e estruturas básicas~~
4. ⏳ Desenvolver funcionalidades core restantes
5. ⏳ Testes e refinamentos
6. 📋 Deploy