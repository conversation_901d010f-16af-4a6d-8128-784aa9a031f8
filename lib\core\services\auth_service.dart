import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../errors/exceptions.dart';
import '../../models/user_model.dart';
import 'firestore_service.dart';

/// Serviço de autenticação
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirestoreService _firestoreService = FirestoreService();

  /// Stream do usuário atual
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// Usuário atual
  User? get currentUser => _auth.currentUser;

  /// Verifica se o usuário está logado
  bool get isLoggedIn => currentUser != null;

  /// Login com Google
  Future<UserModel> signInWithGoogle() async {
    try {
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        throw const AuthException('Login cancelado pelo usuário');
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final UserCredential userCredential = await _auth.signInWithCredential(credential);
      final User? firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        throw const AuthException('Erro ao autenticar com Firebase');
      }

      // Criar ou atualizar usuário no Firestore
      final userModel = UserModel.create(
        id: firebaseUser.uid,
        name: firebaseUser.displayName ?? 'Usuário',
        email: firebaseUser.email ?? '',
        photoUrl: firebaseUser.photoURL,
      );

      // Salvar/atualizar no Firestore
      await _firestoreService.createOrUpdateUser(userModel);

      return userModel;
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getAuthErrorMessage(e.code), code: e.code);
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException('Erro inesperado durante o login: ${e.toString()}');
    }
  }

  /// Logout
  Future<void> signOut() async {
    try {
      await Future.wait([
        _auth.signOut(),
        _googleSignIn.signOut(),
      ]);
    } catch (e) {
      throw AuthException('Erro ao fazer logout: ${e.toString()}');
    }
  }

  /// Obtém o modelo do usuário atual
  Future<UserModel?> getCurrentUserModel() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      return await _firestoreService.getUser(user.uid);
    } catch (e) {
      // Se não encontrar o usuário no Firestore, criar um novo
      final userModel = UserModel.create(
        id: user.uid,
        name: user.displayName ?? 'Usuário',
        email: user.email ?? '',
        photoUrl: user.photoURL,
      );
      
      await _firestoreService.createOrUpdateUser(userModel);
      return userModel;
    }
  }

  /// Atualiza o perfil do usuário
  Future<void> updateProfile({
    String? displayName,
    String? photoURL,
  }) async {
    final user = currentUser;
    if (user == null) throw const AuthException('Usuário não está logado');

    try {
      await user.updateDisplayName(displayName);
      await user.updatePhotoURL(photoURL);

      // Atualizar no Firestore também
      final userModel = await getCurrentUserModel();
      if (userModel != null) {
        final updatedUser = userModel.copyWith(
          name: displayName ?? userModel.name,
          photoUrl: photoURL ?? userModel.photoUrl,
        ).updated();
        
        await _firestoreService.createOrUpdateUser(updatedUser);
      }
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getAuthErrorMessage(e.code), code: e.code);
    } catch (e) {
      throw AuthException('Erro ao atualizar perfil: ${e.toString()}');
    }
  }

  /// Reautentica o usuário (necessário para operações sensíveis)
  Future<void> reauthenticate() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        throw const AuthException('Reautenticação cancelada');
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      await currentUser?.reauthenticateWithCredential(credential);
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getAuthErrorMessage(e.code), code: e.code);
    } catch (e) {
      throw AuthException('Erro na reautenticação: ${e.toString()}');
    }
  }

  /// Deleta a conta do usuário
  Future<void> deleteAccount() async {
    final user = currentUser;
    if (user == null) throw const AuthException('Usuário não está logado');

    try {
      // Reautenticar antes de deletar
      await reauthenticate();
      
      // Deletar dados do Firestore
      await _firestoreService.deleteUser(user.uid);
      
      // Deletar conta do Firebase Auth
      await user.delete();
      
      // Fazer logout do Google
      await _googleSignIn.signOut();
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getAuthErrorMessage(e.code), code: e.code);
    } catch (e) {
      throw AuthException('Erro ao deletar conta: ${e.toString()}');
    }
  }

  /// Converte códigos de erro do Firebase em mensagens amigáveis
  String _getAuthErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'Usuário não encontrado';
      case 'wrong-password':
        return 'Senha incorreta';
      case 'invalid-email':
        return 'Email inválido';
      case 'user-disabled':
        return 'Conta desabilitada';
      case 'too-many-requests':
        return 'Muitas tentativas. Tente novamente mais tarde';
      case 'operation-not-allowed':
        return 'Operação não permitida';
      case 'weak-password':
        return 'Senha muito fraca';
      case 'email-already-in-use':
        return 'Email já está em uso';
      case 'requires-recent-login':
        return 'Operação requer login recente';
      case 'network-request-failed':
        return 'Erro de conexão';
      default:
        return 'Erro de autenticação: $code';
    }
  }
}
