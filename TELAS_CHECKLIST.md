# 📱 Checklist de Telas - Event Task Manager

## 📊 Resumo Geral
- **Total de Telas**: 25 telas
- **✅ Implementadas**: 2 telas (8%)
- **📋 Pendentes**: 23 telas (92%)
- **🎯 Prioridade Alta**: 8 telas

---

## 🔐 Autenticação
- [x] **LoginScreen** - `lib/screens/auth/login_screen.dart` ✅
- [ ] **RegisterScreen** - `lib/screens/auth/register_screen.dart` 📋

## 🏠 Home e Navegação
- [x] **HomeScreen** - `lib/screens/home/<USER>

## 🎯 Eventos - Gerenciamento
- [ ] **CreateEventScreen** - `lib/screens/event/create_event_screen.dart` 🎯
- [ ] **JoinEventScreen** - `lib/screens/event/join_event_screen.dart` 🎯
- [ ] **EventDetailScreen** - `lib/screens/event/event_detail_screen.dart` 🎯

## 📋 Tarefas e Microtarefas
- [ ] **EventTasksScreen** - `lib/screens/event/event_tasks_screen.dart` 🎯
- [ ] **CreateTaskScreen** - `lib/screens/event/create_task_screen.dart` 🎯
- [ ] **TaskDetailScreen** - `lib/screens/event/task_detail_screen.dart`
- [ ] **MicrotaskDetailScreen** - `lib/screens/event/microtask_detail_screen.dart`

## 👥 Gerenciamento de Voluntários
- [ ] **ManageVolunteersScreen** - `lib/screens/event/manage_volunteers_screen.dart` 🎯
- [ ] **VolunteerDetailScreen** - `lib/screens/event/volunteer_detail_screen.dart`

## 📊 Acompanhamento e Relatórios
- [ ] **TrackTasksScreen** - `lib/screens/event/track_tasks_screen.dart` 🎯
- [ ] **AnalyticsScreen** - `lib/screens/event/analytics_screen.dart`

## 👤 Perfil e Configurações
- [ ] **ProfileScreen** - `lib/screens/profile/profile_screen.dart` 🎯
- [ ] **SettingsScreen** - `lib/screens/profile/settings_screen.dart`
- [ ] **EditProfileScreen** - `lib/screens/profile/edit_profile_screen.dart`

## 🔔 Notificações
- [ ] **NotificationsScreen** - `lib/screens/notifications/notifications_screen.dart`
- [ ] **NotificationDetailScreen** - `lib/screens/notifications/notification_detail_screen.dart`

## 🎨 Telas de Apoio
- [ ] **SplashScreen** - `lib/screens/splash/splash_screen.dart`
- [ ] **OnboardingScreen** - `lib/screens/onboarding/onboarding_screen.dart`
- [ ] **ErrorScreen** - `lib/screens/error/error_screen.dart`

---

## 🚀 Plano de Implementação

### **Fase 1 - Core (4 telas) - Semana 1-2**
1. [ ] CreateEventScreen
2. [ ] JoinEventScreen  
3. [ ] EventDetailScreen
4. [ ] ProfileScreen

### **Fase 2 - Tarefas (4 telas) - Semana 3-4**
5. [ ] EventTasksScreen
6. [ ] CreateTaskScreen
7. [ ] TaskDetailScreen
8. [ ] MicrotaskDetailScreen

### **Fase 3 - Voluntários (3 telas) - Semana 5**
9. [ ] ManageVolunteersScreen
10. [ ] VolunteerDetailScreen
11. [ ] TrackTasksScreen

### **Fase 4 - Avançado (3 telas) - Semana 6**
12. [ ] AnalyticsScreen
13. [ ] NotificationsScreen
14. [ ] SettingsScreen

### **Fase 5 - Polimento (6 telas) - Semana 7**
15. [ ] RegisterScreen
16. [ ] EditProfileScreen
17. [ ] NotificationDetailScreen
18. [ ] SplashScreen
19. [ ] OnboardingScreen
20. [ ] ErrorScreen

---

## 📝 Notas de Implementação

### ✅ **Já Implementado:**
- **LoginScreen**: Completa com Google Sign-In
- **HomeScreen**: Estrutura básica, falta lista de eventos

### 🎯 **Próximas Prioridades:**
1. **CreateEventScreen**: Fundamental para gerenciadores
2. **JoinEventScreen**: Fundamental para voluntários
3. **EventDetailScreen**: Hub central de cada evento

### 🔧 **Dependências Técnicas:**
- Todas as telas de evento dependem de EventProvider
- Telas de tarefa dependem de TaskProvider
- Telas de voluntário dependem de VolunteerProvider
- Sistema de roteamento com GoRouter precisa ser implementado

### 📱 **Considerações de UX:**
- Design responsivo para web e mobile
- Estados de loading e erro em todas as telas
- Navegação intuitiva entre telas relacionadas
- Feedback visual para ações do usuário

---

**Legenda:**
- ✅ Implementado
- ⚠️ Parcialmente implementado
- 📋 Pendente
- 🎯 Prioridade alta
