import 'package:cloud_firestore/cloud_firestore.dart';
import 'task_model.dart';

/// Modelo de dados da microtarefa
class MicrotaskModel {
  final String id;
  final String taskId;
  final String eventId;
  final String title;
  final String description;
  final String? assignedTo; // null se não atribuída
  final DateTime? assignedAt;
  final String? assignedBy; // gerenciador que atribuiu
  final int estimatedDuration; // em minutos
  final TaskPriority priority;
  final List<String> requiredSkills;
  final List<String> requiredResources;
  final MicrotaskStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? completedAt;

  const MicrotaskModel({
    required this.id,
    required this.taskId,
    required this.eventId,
    required this.title,
    required this.description,
    this.assignedTo,
    this.assignedAt,
    this.assignedBy,
    required this.estimatedDuration,
    required this.priority,
    required this.requiredSkills,
    required this.requiredResources,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.completedAt,
  });

  /// Cria uma instância a partir de um Map (Firestore)
  factory MicrotaskModel.fromMap(Map<String, dynamic> map) {
    return MicrotaskModel(
      id: map['id'] as String,
      taskId: map['taskId'] as String,
      eventId: map['eventId'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      assignedTo: map['assignedTo'] as String?,
      assignedAt: map['assignedAt'] != null ? (map['assignedAt'] as Timestamp).toDate() : null,
      assignedBy: map['assignedBy'] as String?,
      estimatedDuration: map['estimatedDuration'] as int,
      priority: TaskPriority.fromString(map['priority'] as String),
      requiredSkills: List<String>.from(map['requiredSkills'] as List),
      requiredResources: List<String>.from(map['requiredResources'] as List),
      status: MicrotaskStatus.fromString(map['status'] as String),
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      completedAt: map['completedAt'] != null ? (map['completedAt'] as Timestamp).toDate() : null,
    );
  }

  /// Converte para Map (para salvar no Firestore)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'taskId': taskId,
      'eventId': eventId,
      'title': title,
      'description': description,
      'assignedTo': assignedTo,
      'assignedAt': assignedAt != null ? Timestamp.fromDate(assignedAt!) : null,
      'assignedBy': assignedBy,
      'estimatedDuration': estimatedDuration,
      'priority': priority.value,
      'requiredSkills': requiredSkills,
      'requiredResources': requiredResources,
      'status': status.value,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
    };
  }

  /// Cria uma cópia com campos modificados
  MicrotaskModel copyWith({
    String? id,
    String? taskId,
    String? eventId,
    String? title,
    String? description,
    String? assignedTo,
    DateTime? assignedAt,
    String? assignedBy,
    int? estimatedDuration,
    TaskPriority? priority,
    List<String>? requiredSkills,
    List<String>? requiredResources,
    MicrotaskStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? completedAt,
  }) {
    return MicrotaskModel(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      eventId: eventId ?? this.eventId,
      title: title ?? this.title,
      description: description ?? this.description,
      assignedTo: assignedTo ?? this.assignedTo,
      assignedAt: assignedAt ?? this.assignedAt,
      assignedBy: assignedBy ?? this.assignedBy,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      priority: priority ?? this.priority,
      requiredSkills: requiredSkills ?? this.requiredSkills,
      requiredResources: requiredResources ?? this.requiredResources,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  /// Cria uma nova microtarefa
  factory MicrotaskModel.create({
    required String id,
    required String taskId,
    required String eventId,
    required String title,
    required String description,
    required int estimatedDuration,
    required TaskPriority priority,
    required List<String> requiredSkills,
    required List<String> requiredResources,
  }) {
    final now = DateTime.now();
    return MicrotaskModel(
      id: id,
      taskId: taskId,
      eventId: eventId,
      title: title,
      description: description,
      estimatedDuration: estimatedDuration,
      priority: priority,
      requiredSkills: requiredSkills,
      requiredResources: requiredResources,
      status: MicrotaskStatus.pending,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Atualiza o timestamp de modificação
  MicrotaskModel updated() {
    return copyWith(updatedAt: DateTime.now());
  }

  /// Atribui microtarefa a um voluntário
  MicrotaskModel assignTo(String userId, String assignedById) {
    final now = DateTime.now();
    return copyWith(
      assignedTo: userId,
      assignedAt: now,
      assignedBy: assignedById,
      status: MicrotaskStatus.assigned,
    ).updated();
  }

  /// Remove atribuição da microtarefa
  MicrotaskModel unassign() {
    return copyWith(
      assignedTo: null,
      assignedAt: null,
      assignedBy: null,
      status: MicrotaskStatus.pending,
    ).updated();
  }

  /// Marca microtarefa como em progresso
  MicrotaskModel markInProgress() {
    return copyWith(status: MicrotaskStatus.inProgress).updated();
  }

  /// Marca microtarefa como concluída
  MicrotaskModel markCompleted() {
    final now = DateTime.now();
    return copyWith(
      status: MicrotaskStatus.completed,
      completedAt: now,
    ).updated();
  }

  /// Verifica se a microtarefa está atribuída
  bool get isAssigned => assignedTo != null;

  /// Verifica se a microtarefa está disponível para atribuição
  bool get isAvailable => status == MicrotaskStatus.pending && !isAssigned;

  /// Verifica se a microtarefa está em progresso
  bool get isInProgress => status == MicrotaskStatus.inProgress;

  /// Verifica se a microtarefa está concluída
  bool get isCompleted => status == MicrotaskStatus.completed;

  /// Calcula o tempo gasto na microtarefa (se concluída)
  Duration? get timeSpent {
    if (assignedAt == null || completedAt == null) return null;
    return completedAt!.difference(assignedAt!);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MicrotaskModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MicrotaskModel(id: $id, title: $title, status: $status, assignedTo: $assignedTo, priority: $priority)';
  }
}

/// Status da microtarefa
enum MicrotaskStatus {
  pending('pending'),
  assigned('assigned'),
  inProgress('in_progress'),
  completed('completed');

  const MicrotaskStatus(this.value);
  final String value;

  static MicrotaskStatus fromString(String value) {
    return MicrotaskStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => MicrotaskStatus.pending,
    );
  }

  @override
  String toString() => value;
}
