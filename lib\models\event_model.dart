import 'package:cloud_firestore/cloud_firestore.dart';
import 'volunteer_model.dart';

/// Modelo de dados do evento
class EventModel {
  final String id;
  final String name;
  final String description;
  final String location;
  final String eventTag;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> requiredSkills;
  final List<String> requiredResources;
  final List<String> managers;
  final List<VolunteerModel> volunteers;
  final EventStatus status;

  const EventModel({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.eventTag,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    required this.requiredSkills,
    required this.requiredResources,
    required this.managers,
    required this.volunteers,
    required this.status,
  });

  /// Cria uma instância a partir de um Map (Firestore)
  factory EventModel.fromMap(Map<String, dynamic> map) {
    return EventModel(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String,
      location: map['location'] as String,
      eventTag: map['eventTag'] as String,
      createdBy: map['createdBy'] as String,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      requiredSkills: List<String>.from(map['requiredSkills'] as List),
      requiredResources: List<String>.from(map['requiredResources'] as List),
      managers: List<String>.from(map['managers'] as List),
      volunteers: (map['volunteers'] as List)
          .map((v) => VolunteerModel.fromMap(v as Map<String, dynamic>))
          .toList(),
      status: EventStatus.fromString(map['status'] as String),
    );
  }

  /// Converte para Map (para salvar no Firestore)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'location': location,
      'eventTag': eventTag,
      'createdBy': createdBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'requiredSkills': requiredSkills,
      'requiredResources': requiredResources,
      'managers': managers,
      'volunteers': volunteers.map((v) => v.toMap()).toList(),
      'status': status.value,
    };
  }

  /// Cria uma cópia com campos modificados
  EventModel copyWith({
    String? id,
    String? name,
    String? description,
    String? location,
    String? eventTag,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? requiredSkills,
    List<String>? requiredResources,
    List<String>? managers,
    List<VolunteerModel>? volunteers,
    EventStatus? status,
  }) {
    return EventModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      location: location ?? this.location,
      eventTag: eventTag ?? this.eventTag,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      requiredSkills: requiredSkills ?? this.requiredSkills,
      requiredResources: requiredResources ?? this.requiredResources,
      managers: managers ?? this.managers,
      volunteers: volunteers ?? this.volunteers,
      status: status ?? this.status,
    );
  }

  /// Cria um novo evento
  factory EventModel.create({
    required String id,
    required String name,
    required String description,
    required String location,
    required String eventTag,
    required String createdBy,
    required List<String> requiredSkills,
    required List<String> requiredResources,
  }) {
    final now = DateTime.now();
    return EventModel(
      id: id,
      name: name,
      description: description,
      location: location,
      eventTag: eventTag,
      createdBy: createdBy,
      createdAt: now,
      updatedAt: now,
      requiredSkills: requiredSkills,
      requiredResources: requiredResources,
      managers: [createdBy], // Criador é automaticamente gerenciador
      volunteers: [],
      status: EventStatus.active,
    );
  }

  /// Atualiza o timestamp de modificação
  EventModel updated() {
    return copyWith(updatedAt: DateTime.now());
  }

  /// Adiciona um voluntário ao evento
  EventModel addVolunteer(VolunteerModel volunteer) {
    final updatedVolunteers = List<VolunteerModel>.from(volunteers);
    
    // Remove voluntário existente se houver
    updatedVolunteers.removeWhere((v) => v.userId == volunteer.userId);
    
    // Adiciona o novo/atualizado voluntário
    updatedVolunteers.add(volunteer);
    
    return copyWith(volunteers: updatedVolunteers).updated();
  }

  /// Remove um voluntário do evento
  EventModel removeVolunteer(String userId) {
    final updatedVolunteers = volunteers.where((v) => v.userId != userId).toList();
    return copyWith(volunteers: updatedVolunteers).updated();
  }

  /// Adiciona um gerenciador ao evento
  EventModel addManager(String userId) {
    if (managers.contains(userId)) return this;
    
    final updatedManagers = List<String>.from(managers)..add(userId);
    return copyWith(managers: updatedManagers).updated();
  }

  /// Remove um gerenciador do evento
  EventModel removeManager(String userId) {
    if (userId == createdBy) return this; // Não pode remover o criador
    
    final updatedManagers = managers.where((id) => id != userId).toList();
    return copyWith(managers: updatedManagers).updated();
  }

  /// Verifica se um usuário é gerenciador
  bool isManager(String userId) {
    return managers.contains(userId);
  }

  /// Verifica se um usuário é voluntário
  bool isVolunteer(String userId) {
    return volunteers.any((v) => v.userId == userId);
  }

  /// Obtém um voluntário específico
  VolunteerModel? getVolunteer(String userId) {
    try {
      return volunteers.firstWhere((v) => v.userId == userId);
    } catch (e) {
      return null;
    }
  }

  /// Obtém voluntários ativos
  List<VolunteerModel> get activeVolunteers {
    return volunteers.where((v) => v.status == VolunteerStatus.active).toList();
  }

  /// Obtém total de voluntários
  int get totalVolunteers => volunteers.length;

  /// Obtém total de voluntários ativos
  int get totalActiveVolunteers => activeVolunteers.length;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EventModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'EventModel(id: $id, name: $name, eventTag: $eventTag, status: $status, volunteers: ${volunteers.length})';
  }
}

/// Status do evento
enum EventStatus {
  active('active'),
  completed('completed'),
  cancelled('cancelled');

  const EventStatus(this.value);
  final String value;

  static EventStatus fromString(String value) {
    return EventStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => EventStatus.active,
    );
  }

  @override
  String toString() => value;
}
