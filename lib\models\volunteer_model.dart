import 'package:cloud_firestore/cloud_firestore.dart';

/// Modelo de dados do voluntário (usado dentro do EventModel)
class VolunteerModel {
  final String userId;
  final DateTime joinedAt;
  final List<String> availableDays;
  final AvailableHours availableHours;
  final List<String> skills;
  final List<String> resources;
  final VolunteerStatus status;

  const VolunteerModel({
    required this.userId,
    required this.joinedAt,
    required this.availableDays,
    required this.availableHours,
    required this.skills,
    required this.resources,
    required this.status,
  });

  /// Cria uma instância a partir de um Map
  factory VolunteerModel.fromMap(Map<String, dynamic> map) {
    return VolunteerModel(
      userId: map['userId'] as String,
      joinedAt: (map['joinedAt'] as Timestamp).toDate(),
      availableDays: List<String>.from(map['availableDays'] as List),
      availableHours: AvailableHours.fromMap(map['availableHours'] as Map<String, dynamic>),
      skills: List<String>.from(map['skills'] as List),
      resources: List<String>.from(map['resources'] as List),
      status: VolunteerStatus.fromString(map['status'] as String),
    );
  }

  /// Converte para Map
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'joinedAt': Timestamp.fromDate(joinedAt),
      'availableDays': availableDays,
      'availableHours': availableHours.toMap(),
      'skills': skills,
      'resources': resources,
      'status': status.value,
    };
  }

  /// Cria uma cópia com campos modificados
  VolunteerModel copyWith({
    String? userId,
    DateTime? joinedAt,
    List<String>? availableDays,
    AvailableHours? availableHours,
    List<String>? skills,
    List<String>? resources,
    VolunteerStatus? status,
  }) {
    return VolunteerModel(
      userId: userId ?? this.userId,
      joinedAt: joinedAt ?? this.joinedAt,
      availableDays: availableDays ?? this.availableDays,
      availableHours: availableHours ?? this.availableHours,
      skills: skills ?? this.skills,
      resources: resources ?? this.resources,
      status: status ?? this.status,
    );
  }

  /// Cria um novo voluntário
  factory VolunteerModel.create({
    required String userId,
    required List<String> availableDays,
    required AvailableHours availableHours,
    required List<String> skills,
    required List<String> resources,
  }) {
    return VolunteerModel(
      userId: userId,
      joinedAt: DateTime.now(),
      availableDays: availableDays,
      availableHours: availableHours,
      skills: skills,
      resources: resources,
      status: VolunteerStatus.active,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VolunteerModel && other.userId == userId;
  }

  @override
  int get hashCode => userId.hashCode;

  @override
  String toString() {
    return 'VolunteerModel(userId: $userId, joinedAt: $joinedAt, availableDays: $availableDays, availableHours: $availableHours, skills: $skills, resources: $resources, status: $status)';
  }
}

/// Horários disponíveis do voluntário
class AvailableHours {
  final String start;
  final String end;

  const AvailableHours({
    required this.start,
    required this.end,
  });

  factory AvailableHours.fromMap(Map<String, dynamic> map) {
    return AvailableHours(
      start: map['start'] as String,
      end: map['end'] as String,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'start': start,
      'end': end,
    };
  }

  AvailableHours copyWith({
    String? start,
    String? end,
  }) {
    return AvailableHours(
      start: start ?? this.start,
      end: end ?? this.end,
    );
  }

  /// Calcula a duração em minutos
  int get durationInMinutes {
    final startTime = _parseTime(start);
    final endTime = _parseTime(end);
    
    if (startTime == null || endTime == null) return 0;
    
    return endTime.difference(startTime).inMinutes;
  }

  /// Verifica se um horário está dentro do intervalo disponível
  bool isTimeAvailable(String time) {
    final checkTime = _parseTime(time);
    final startTime = _parseTime(start);
    final endTime = _parseTime(end);
    
    if (checkTime == null || startTime == null || endTime == null) return false;
    
    return checkTime.isAfter(startTime) && checkTime.isBefore(endTime);
  }

  DateTime? _parseTime(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length != 2) return null;
      
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);
      
      final now = DateTime.now();
      return DateTime(now.year, now.month, now.day, hour, minute);
    } catch (e) {
      return null;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AvailableHours && other.start == start && other.end == end;
  }

  @override
  int get hashCode => start.hashCode ^ end.hashCode;

  @override
  String toString() => 'AvailableHours(start: $start, end: $end)';
}

/// Status do voluntário
enum VolunteerStatus {
  active('active'),
  inactive('inactive');

  const VolunteerStatus(this.value);
  final String value;

  static VolunteerStatus fromString(String value) {
    return VolunteerStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => VolunteerStatus.active,
    );
  }

  @override
  String toString() => value;
}
