import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/utils/extensions.dart';
import '../../core/utils/validators.dart';
import '../../providers/auth_provider.dart';
import '../../providers/event_provider.dart';
import '../../models/event_model.dart';
import '../../models/volunteer_model.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_widget.dart';

/// Tela para entrada em eventos via TAG
class JoinEventScreen extends StatefulWidget {
  const JoinEventScreen({super.key});

  @override
  State<JoinEventScreen> createState() => _JoinEventScreenState();
}

class _JoinEventScreenState extends State<JoinEventScreen> {
  final _formKey = GlobalKey<FormState>();
  final _tagController = TextEditingController();

  EventModel? _foundEvent;
  bool _isSearching = false;

  // Dados do voluntário
  final List<String> _selectedDays = [];
  TimeOfDay _startTime = const TimeOfDay(hour: 9, minute: 0);
  TimeOfDay _endTime = const TimeOfDay(hour: 17, minute: 0);
  final List<String> _userSkills = [];
  final List<String> _userResources = [];
  final TextEditingController _skillController = TextEditingController();
  final TextEditingController _resourceController = TextEditingController();

  final List<String> _weekDays = [
    'Segunda-feira',
    'Terça-feira',
    'Quarta-feira',
    'Quinta-feira',
    'Sexta-feira',
    'Sábado',
    'Domingo',
  ];

  @override
  void dispose() {
    _tagController.dispose();
    _skillController.dispose();
    _resourceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: _buildAppBar(), body: _buildBody());
  }

  /// Constrói a AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('Entrar em Evento'),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
    );
  }

  /// Constrói o corpo da tela
  Widget _buildBody() {
    return Consumer<EventProvider>(
      builder: (context, eventProvider, child) {
        if (eventProvider.isLoading) {
          return const Center(child: LoadingWidget(message: 'Processando...'));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSearchSection(),
              if (_foundEvent != null) ...[
                const SizedBox(height: 24),
                _buildEventInfo(),
                const SizedBox(height: 24),
                _buildVolunteerForm(),
              ],
            ],
          ),
        );
      },
    );
  }

  /// Seção de busca por TAG
  Widget _buildSearchSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Buscar Evento',
          style: context.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Digite a TAG do evento para participar',
          style: context.textTheme.bodyMedium?.copyWith(
            color: AppColors.onBackground.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _tagController,
                label: 'TAG do Evento',
                hint: 'Ex: ABC123',
                validator: Validators.required,
                prefixIcon: Icons.tag,
                textCapitalization: TextCapitalization.characters,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _isSearching ? null : _searchEvent,
              icon: _isSearching
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.search),
              style: IconButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.onPrimary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Informações do evento encontrado
  Widget _buildEventInfo() {
    if (_foundEvent == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _foundEvent!.name,
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(_foundEvent!.description, style: context.textTheme.bodyMedium),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(
                  Icons.location_on,
                  size: 16,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    _foundEvent!.location,
                    style: context.textTheme.bodySmall,
                  ),
                ),
              ],
            ),
            if (_foundEvent!.requiredSkills.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Habilidades Necessárias:',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 4,
                children: _foundEvent!.requiredSkills.map((skill) {
                  return Chip(
                    label: Text(skill),
                    backgroundColor: AppColors.secondaryContainer,
                    labelStyle: const TextStyle(
                      color: AppColors.onSecondaryContainer,
                      fontSize: 12,
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Formulário do voluntário
  Widget _buildVolunteerForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Suas Informações',
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 16),
          _buildAvailabilitySection(),
          const SizedBox(height: 24),
          _buildSkillsSection(),
          const SizedBox(height: 24),
          _buildResourcesSection(),
          const SizedBox(height: 32),
          _buildJoinButton(),
        ],
      ),
    );
  }

  /// Seção de disponibilidade
  Widget _buildAvailabilitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Disponibilidade',
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Dias disponíveis:',
          style: context.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: _weekDays.map((day) {
            final isSelected = _selectedDays.contains(day);
            return FilterChip(
              label: Text(day),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedDays.add(day);
                  } else {
                    _selectedDays.remove(day);
                  }
                });
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
        Text(
          'Horário disponível:',
          style: context.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: ListTile(
                title: const Text('Início'),
                subtitle: Text(_startTime.format(context)),
                leading: const Icon(Icons.access_time),
                onTap: () => _selectTime(true),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.grey.shade300),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ListTile(
                title: const Text('Fim'),
                subtitle: Text(_endTime.format(context)),
                leading: const Icon(Icons.access_time),
                onTap: () => _selectTime(false),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.grey.shade300),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Seção de habilidades do usuário
  Widget _buildSkillsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Suas Habilidades',
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildAddItemField(
          controller: _skillController,
          hint: 'Digite uma habilidade',
          onAdd: _addSkill,
        ),
        const SizedBox(height: 16),
        _buildItemsList(_userSkills, _removeSkill),
      ],
    );
  }

  /// Seção de recursos do usuário
  Widget _buildResourcesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Seus Recursos',
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildAddItemField(
          controller: _resourceController,
          hint: 'Digite um recurso',
          onAdd: _addResource,
        ),
        const SizedBox(height: 16),
        _buildItemsList(_userResources, _removeResource),
      ],
    );
  }

  /// Campo para adicionar item
  Widget _buildAddItemField({
    required TextEditingController controller,
    required String hint,
    required VoidCallback onAdd,
  }) {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: controller,
            decoration: InputDecoration(
              hintText: hint,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onSubmitted: (_) => onAdd(),
          ),
        ),
        const SizedBox(width: 8),
        IconButton(
          onPressed: onAdd,
          icon: const Icon(Icons.add),
          style: IconButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.onPrimary,
          ),
        ),
      ],
    );
  }

  /// Lista de itens
  Widget _buildItemsList(List<String> items, Function(int) onRemove) {
    if (items.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          'Nenhum item adicionado',
          style: context.textTheme.bodyMedium?.copyWith(
            color: AppColors.onBackground.withOpacity(0.5),
          ),
        ),
      );
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: items.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;

        return Chip(
          label: Text(item),
          deleteIcon: const Icon(Icons.close, size: 18),
          onDeleted: () => onRemove(index),
          backgroundColor: AppColors.primaryContainer,
          labelStyle: const TextStyle(color: AppColors.onPrimaryContainer),
        );
      }).toList(),
    );
  }

  /// Botão de entrar no evento
  Widget _buildJoinButton() {
    return Consumer<EventProvider>(
      builder: (context, eventProvider, child) {
        return SizedBox(
          width: double.infinity,
          child: CustomButton(
            text: 'Entrar no Evento',
            onPressed: eventProvider.isLoading ? null : _handleJoinEvent,
            isLoading: eventProvider.isLoading,
          ),
        );
      },
    );
  }

  /// Busca evento por TAG
  Future<void> _searchEvent() async {
    final tag = _tagController.text.trim().toUpperCase();
    if (tag.isEmpty) return;

    setState(() {
      _isSearching = true;
    });

    final eventProvider = context.read<EventProvider>();
    final event = await eventProvider.findEventByTag(tag);

    setState(() {
      _isSearching = false;
      _foundEvent = event;
    });

    if (event == null && mounted) {
      context.showSnackBar('Evento não encontrado');
    }
  }

  /// Seleciona horário
  Future<void> _selectTime(bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime ? _startTime : _endTime,
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _startTime = picked;
        } else {
          _endTime = picked;
        }
      });
    }
  }

  /// Adiciona habilidade
  void _addSkill() {
    final skill = _skillController.text.trim();
    if (skill.isNotEmpty && !_userSkills.contains(skill)) {
      setState(() {
        _userSkills.add(skill);
        _skillController.clear();
      });
    }
  }

  /// Remove habilidade
  void _removeSkill(int index) {
    setState(() {
      _userSkills.removeAt(index);
    });
  }

  /// Adiciona recurso
  void _addResource() {
    final resource = _resourceController.text.trim();
    if (resource.isNotEmpty && !_userResources.contains(resource)) {
      setState(() {
        _userResources.add(resource);
        _resourceController.clear();
      });
    }
  }

  /// Remove recurso
  void _removeResource(int index) {
    setState(() {
      _userResources.removeAt(index);
    });
  }

  /// Manipula entrada no evento
  Future<void> _handleJoinEvent() async {
    if (_foundEvent == null) return;
    if (_selectedDays.isEmpty) {
      context.showSnackBar('Selecione pelo menos um dia disponível');
      return;
    }

    final authProvider = context.read<AuthProvider>();
    final eventProvider = context.read<EventProvider>();
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      context.showSnackBar('Usuário não autenticado');
      return;
    }

    final availableHours = AvailableHours(
      start:
          '${_startTime.hour.toString().padLeft(2, '0')}:${_startTime.minute.toString().padLeft(2, '0')}',
      end:
          '${_endTime.hour.toString().padLeft(2, '0')}:${_endTime.minute.toString().padLeft(2, '0')}',
    );

    final success = await eventProvider.joinEvent(
      eventId: _foundEvent!.id,
      user: currentUser,
      availableDays: _selectedDays,
      availableHours: availableHours,
      skills: _userSkills,
      resources: _userResources,
    );

    if (success && mounted) {
      context.showSnackBar('Você entrou no evento com sucesso!');
      Navigator.of(context).pop();
    } else if (mounted && eventProvider.errorMessage != null) {
      context.showSnackBar(eventProvider.errorMessage!);
    }
  }
}
