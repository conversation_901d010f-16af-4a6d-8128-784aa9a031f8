# Implementação do signInWithGoogle para Web

## Resumo da Implementação

A função `signInWithGoogle` foi implementada de acordo com o exemplo fornecido, com suporte específico para web usando `signInWithPopup` e mantendo a compatibilidade com mobile/desktop usando `GoogleSignIn`.

## Principais Mudanças

### 1. AuthService - signInWithGoogle()

```dart
/// Login com Google
Future<UserModel> signInWithGoogle() async {
  try {
    UserCredential userCredential;
    
    if (kIsWeb) {
      // Implementação específica para Web usando signInWithPopup
      // Cria uma instância do provedor de autenticação do Google
      GoogleAuthProvider googleProvider = GoogleAuthProvider();

      // Você pode adicionar escopos para solicitar permissões adicionais
      // Exemplo: para ler os contatos do usuário
      googleProvider.addScope(
        'https://www.googleapis.com/auth/contacts.readonly',
      );
      googleProvider.setCustomParameters({'login_hint': '<EMAIL>'});

      // Faz o login com um Pop-up. O Firebase gerencia o fluxo para você
      userCredential = await _auth.signInWithPopup(googleProvider);
    } else {
      // Implementação para Mobile/Desktop usando GoogleSignIn
      // ... código existente para mobile
    }

    // Salvar dados adicionais do usuário no Firestore
    final userModel = UserModel.create(
      id: firebaseUser.uid,
      name: firebaseUser.displayName ?? 'Sem Nome',
      email: firebaseUser.email ?? 'Sem E-mail',
      photoUrl: firebaseUser.photoURL,
    );

    await _firestoreService.createOrUpdateUser(userModel);
    return userModel;
  } on FirebaseAuthException catch (e) {
    // Trata erros específicos do Firebase Auth
    throw AuthException('Erro ao fazer login com Google: ${e.message}');
  } catch (error) {
    // Trata outros possíveis erros
    throw AuthException('Erro ao fazer login com Google: $error');
  }
}
```

### 2. Método de Reautenticação Atualizado

O método `reauthenticate()` também foi atualizado para suportar web:

```dart
/// Reautentica o usuário (necessário para operações sensíveis)
Future<void> reauthenticate() async {
  try {
    AuthCredential credential;
    
    if (kIsWeb) {
      // Para web, usar signInWithPopup para reautenticação
      GoogleAuthProvider googleProvider = GoogleAuthProvider();
      final UserCredential userCredential = await _auth.signInWithPopup(googleProvider);
      
      if (userCredential.credential == null) {
        throw const AuthException('Reautenticação cancelada');
      }
      
      credential = userCredential.credential!;
    } else {
      // Para mobile/desktop, usar GoogleSignIn
      // ... código existente para mobile
    }

    await currentUser?.reauthenticateWithCredential(credential);
  } catch (e) {
    throw AuthException('Erro na reautenticação: ${e.toString()}');
  }
}
```

## Características da Implementação

### Para Web (kIsWeb == true):
1. ✅ Usa `GoogleAuthProvider()` diretamente
2. ✅ Adiciona scopes opcionais (`contacts.readonly`)
3. ✅ Configura parâmetros customizados (`login_hint`)
4. ✅ Usa `_auth.signInWithPopup(googleProvider)`
5. ✅ Salva dados do usuário no Firestore
6. ✅ Tratamento de erros específico do Firebase Auth

### Para Mobile/Desktop (kIsWeb == false):
1. ✅ Mantém implementação existente com `GoogleSignIn`
2. ✅ Usa fluxo de credential tradicional
3. ✅ Compatibilidade total com código existente

## Como Testar

### 1. Teste em Ambiente Web
```bash
flutter run -d chrome
```

### 2. Teste em Ambiente Mobile
```bash
flutter run -d android
# ou
flutter run -d ios
```

### 3. Executar Testes Unitários
```bash
flutter test test/auth_service_test.dart
```

## Benefícios da Implementação

1. **Compatibilidade Multiplataforma**: Funciona tanto na web quanto em mobile/desktop
2. **Detecção Automática**: Usa `kIsWeb` para detectar a plataforma automaticamente
3. **Código Limpo**: Mantém a mesma interface pública
4. **Tratamento de Erros**: Erros específicos para cada plataforma
5. **Flexibilidade**: Permite configuração de scopes e parâmetros customizados

## Configuração Adicional Necessária

Para que funcione completamente na web, certifique-se de que:

1. **Firebase Web Config**: O arquivo `firebase_options.dart` está configurado corretamente
2. **Web Dependencies**: As dependências do Firebase Auth Web estão instaladas
3. **Domain Authorization**: O domínio está autorizado no Firebase Console

## Próximos Passos

1. Testar a implementação em ambiente web
2. Configurar domínios autorizados no Firebase Console
3. Adicionar testes de integração mais completos
4. Considerar adicionar mais scopes conforme necessário
