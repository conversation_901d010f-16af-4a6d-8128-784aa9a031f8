import 'package:cloud_firestore/cloud_firestore.dart';

/// Modelo de dados da tarefa
class TaskModel {
  final String id;
  final String eventId;
  final String title;
  final String description;
  final TaskPriority priority;
  final int estimatedDuration; // em minutos
  final List<String> requiredSkills;
  final List<String> requiredResources;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final TaskStatus status;
  final DateTime? dueDate;

  const TaskModel({
    required this.id,
    required this.eventId,
    required this.title,
    required this.description,
    required this.priority,
    required this.estimatedDuration,
    required this.requiredSkills,
    required this.requiredResources,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    this.dueDate,
  });

  /// Cria uma instância a partir de um Map (Firestore)
  factory TaskModel.fromMap(Map<String, dynamic> map) {
    return TaskModel(
      id: map['id'] as String,
      eventId: map['eventId'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      priority: TaskPriority.fromString(map['priority'] as String),
      estimatedDuration: map['estimatedDuration'] as int,
      requiredSkills: List<String>.from(map['requiredSkills'] as List),
      requiredResources: List<String>.from(map['requiredResources'] as List),
      createdBy: map['createdBy'] as String,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      status: TaskStatus.fromString(map['status'] as String),
      dueDate: map['dueDate'] != null ? (map['dueDate'] as Timestamp).toDate() : null,
    );
  }

  /// Converte para Map (para salvar no Firestore)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'eventId': eventId,
      'title': title,
      'description': description,
      'priority': priority.value,
      'estimatedDuration': estimatedDuration,
      'requiredSkills': requiredSkills,
      'requiredResources': requiredResources,
      'createdBy': createdBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'status': status.value,
      'dueDate': dueDate != null ? Timestamp.fromDate(dueDate!) : null,
    };
  }

  /// Cria uma cópia com campos modificados
  TaskModel copyWith({
    String? id,
    String? eventId,
    String? title,
    String? description,
    TaskPriority? priority,
    int? estimatedDuration,
    List<String>? requiredSkills,
    List<String>? requiredResources,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    TaskStatus? status,
    DateTime? dueDate,
  }) {
    return TaskModel(
      id: id ?? this.id,
      eventId: eventId ?? this.eventId,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      requiredSkills: requiredSkills ?? this.requiredSkills,
      requiredResources: requiredResources ?? this.requiredResources,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      dueDate: dueDate ?? this.dueDate,
    );
  }

  /// Cria uma nova tarefa
  factory TaskModel.create({
    required String id,
    required String eventId,
    required String title,
    required String description,
    required TaskPriority priority,
    required int estimatedDuration,
    required List<String> requiredSkills,
    required List<String> requiredResources,
    required String createdBy,
    DateTime? dueDate,
  }) {
    final now = DateTime.now();
    return TaskModel(
      id: id,
      eventId: eventId,
      title: title,
      description: description,
      priority: priority,
      estimatedDuration: estimatedDuration,
      requiredSkills: requiredSkills,
      requiredResources: requiredResources,
      createdBy: createdBy,
      createdAt: now,
      updatedAt: now,
      status: TaskStatus.pending,
      dueDate: dueDate,
    );
  }

  /// Atualiza o timestamp de modificação
  TaskModel updated() {
    return copyWith(updatedAt: DateTime.now());
  }

  /// Marca tarefa como em progresso
  TaskModel markInProgress() {
    return copyWith(status: TaskStatus.inProgress).updated();
  }

  /// Marca tarefa como concluída
  TaskModel markCompleted() {
    return copyWith(status: TaskStatus.completed).updated();
  }

  /// Verifica se a tarefa está atrasada
  bool get isOverdue {
    if (dueDate == null || status == TaskStatus.completed) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  /// Verifica se a tarefa está próxima do vencimento (24h)
  bool get isDueSoon {
    if (dueDate == null || status == TaskStatus.completed) return false;
    final now = DateTime.now();
    final timeDiff = dueDate!.difference(now);
    return timeDiff.inHours <= 24 && timeDiff.inHours > 0;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TaskModel(id: $id, title: $title, priority: $priority, status: $status, estimatedDuration: $estimatedDuration)';
  }
}

/// Prioridade da tarefa
enum TaskPriority {
  high('high'),
  medium('medium'),
  low('low');

  const TaskPriority(this.value);
  final String value;

  static TaskPriority fromString(String value) {
    return TaskPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => TaskPriority.medium,
    );
  }

  /// Retorna o peso numérico da prioridade (para ordenação)
  int get weight {
    switch (this) {
      case TaskPriority.high:
        return 3;
      case TaskPriority.medium:
        return 2;
      case TaskPriority.low:
        return 1;
    }
  }

  @override
  String toString() => value;
}

/// Status da tarefa
enum TaskStatus {
  pending('pending'),
  inProgress('in_progress'),
  completed('completed');

  const TaskStatus(this.value);
  final String value;

  static TaskStatus fromString(String value) {
    return TaskStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => TaskStatus.pending,
    );
  }

  @override
  String toString() => value;
}
