/// Strings do aplicativo
class AppStrings {
  // App
  static const String appName = 'Event Task Manager';
  static const String appDescription = 'Gerenciamento colaborativo de tarefas em eventos';

  // Autenticação
  static const String login = 'Entrar';
  static const String logout = 'Sair';
  static const String loginWithGoogle = 'Entrar com Google';
  static const String welcome = 'Bem-vindo';
  static const String welcomeBack = 'Bem-vindo de volta';
  static const String loginRequired = 'Login necessário';
  static const String loginError = 'Erro ao fazer login';
  static const String logoutError = 'Erro ao fazer logout';

  // Navegação
  static const String home = 'Início';
  static const String events = 'Eventos';
  static const String profile = 'Perfil';
  static const String settings = 'Configurações';

  // Eventos
  static const String createEvent = 'Criar Evento';
  static const String joinEvent = 'Entrar no Evento';
  static const String eventDetails = 'Detalhes do Evento';
  static const String eventName = 'Nome do Evento';
  static const String eventDescription = 'Descrição';
  static const String eventLocation = 'Local';
  static const String eventTag = 'TAG do Evento';
  static const String enterEventTag = 'Digite a TAG do evento';
  static const String eventCreated = 'Evento criado com sucesso';
  static const String eventJoined = 'Você entrou no evento';
  static const String eventNotFound = 'Evento não encontrado';
  static const String myEvents = 'Meus Eventos';
  static const String managedEvents = 'Eventos Gerenciados';
  static const String joinedEvents = 'Eventos Participando';

  // Tarefas
  static const String tasks = 'Tarefas';
  static const String createTask = 'Criar Tarefa';
  static const String taskTitle = 'Título da Tarefa';
  static const String taskDescription = 'Descrição da Tarefa';
  static const String taskPriority = 'Prioridade';
  static const String estimatedDuration = 'Duração Estimada (min)';
  static const String requiredSkills = 'Habilidades Necessárias';
  static const String requiredResources = 'Recursos Necessários';
  static const String dueDate = 'Data de Vencimento';
  static const String assignedTo = 'Atribuído para';
  static const String taskCreated = 'Tarefa criada com sucesso';
  static const String taskUpdated = 'Tarefa atualizada';
  static const String taskCompleted = 'Tarefa concluída';

  // Microtarefas
  static const String microtasks = 'Microtarefas';
  static const String createMicrotask = 'Criar Microtarefa';
  static const String assignMicrotask = 'Atribuir Microtarefa';
  static const String autoAssign = 'Atribuição Automática';
  static const String manualAssign = 'Atribuição Manual';
  static const String microtaskAssigned = 'Microtarefa atribuída';
  static const String myMicrotasks = 'Minhas Microtarefas';

  // Voluntários
  static const String volunteers = 'Voluntários';
  static const String manageVolunteers = 'Gerenciar Voluntários';
  static const String volunteerProfile = 'Perfil do Voluntário';
  static const String availability = 'Disponibilidade';
  static const String skills = 'Habilidades';
  static const String resources = 'Recursos';
  static const String promoteToManager = 'Promover a Gerenciador';
  static const String volunteerPromoted = 'Voluntário promovido';

  // Status
  static const String pending = 'Pendente';
  static const String inProgress = 'Em Andamento';
  static const String completed = 'Concluído';
  static const String cancelled = 'Cancelado';
  static const String active = 'Ativo';
  static const String inactive = 'Inativo';

  // Prioridades
  static const String high = 'Alta';
  static const String medium = 'Média';
  static const String low = 'Baixa';

  // Ações
  static const String create = 'Criar';
  static const String edit = 'Editar';
  static const String delete = 'Excluir';
  static const String save = 'Salvar';
  static const String cancel = 'Cancelar';
  static const String confirm = 'Confirmar';
  static const String yes = 'Sim';
  static const String no = 'Não';
  static const String ok = 'OK';
  static const String close = 'Fechar';
  static const String back = 'Voltar';
  static const String next = 'Próximo';
  static const String finish = 'Finalizar';
  static const String submit = 'Enviar';
  static const String update = 'Atualizar';
  static const String refresh = 'Atualizar';

  // Mensagens de erro
  static const String errorGeneric = 'Ocorreu um erro inesperado';
  static const String errorNetwork = 'Erro de conexão';
  static const String errorPermission = 'Permissão negada';
  static const String errorNotFound = 'Item não encontrado';
  static const String errorInvalidData = 'Dados inválidos';
  static const String errorRequired = 'Campo obrigatório';
  static const String errorInvalidEmail = 'Email inválido';
  static const String errorInvalidTag = 'TAG inválida';

  // Mensagens de sucesso
  static const String successSaved = 'Salvo com sucesso';
  static const String successUpdated = 'Atualizado com sucesso';
  static const String successDeleted = 'Excluído com sucesso';

  // Mensagens de confirmação
  static const String confirmDelete = 'Tem certeza que deseja excluir?';
  static const String confirmCancel = 'Tem certeza que deseja cancelar?';
  static const String confirmLogout = 'Tem certeza que deseja sair?';

  // Placeholders
  static const String searchPlaceholder = 'Pesquisar...';
  static const String noDataAvailable = 'Nenhum dado disponível';
  static const String loading = 'Carregando...';
  static const String noEventsFound = 'Nenhum evento encontrado';
  static const String noTasksFound = 'Nenhuma tarefa encontrada';
  static const String noVolunteersFound = 'Nenhum voluntário encontrado';

  // Dias da semana
  static const String monday = 'Segunda-feira';
  static const String tuesday = 'Terça-feira';
  static const String wednesday = 'Quarta-feira';
  static const String thursday = 'Quinta-feira';
  static const String friday = 'Sexta-feira';
  static const String saturday = 'Sábado';
  static const String sunday = 'Domingo';

  // Métricas
  static const String totalEvents = 'Total de Eventos';
  static const String totalTasks = 'Total de Tarefas';
  static const String completionRate = 'Taxa de Conclusão';
  static const String averageTime = 'Tempo Médio';
  static const String activeVolunteers = 'Voluntários Ativos';
}
