/// Exceções customizadas do aplicativo
abstract class AppException implements Exception {
  final String message;
  final String? code;
  
  const AppException(this.message, {this.code});
  
  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exceção de autenticação
class AuthException extends AppException {
  const AuthException(super.message, {super.code});
}

/// Exceção de rede/conectividade
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code});
}

/// Exceção de dados/validação
class DataException extends AppException {
  const DataException(super.message, {super.code});
}

/// Exceção de permissão
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code});
}

/// Exceção de recurso não encontrado
class NotFoundException extends AppException {
  const NotFoundException(super.message, {super.code});
}

/// Exceção de operação inválida
class InvalidOperationException extends AppException {
  const InvalidOperationException(super.message, {super.code});
}

/// Exceção de Firebase
class FirebaseException extends AppException {
  const FirebaseException(super.message, {super.code});
}

/// Exceção de cache
class CacheException extends AppException {
  const CacheException(super.message, {super.code});
}

/// Exceção de timeout
class TimeoutException extends AppException {
  const TimeoutException(super.message, {super.code});
}

/// Factory para criar exceções baseadas em códigos de erro
class ExceptionFactory {
  static AppException fromCode(String code, String message) {
    switch (code) {
      case 'auth/user-not-found':
      case 'auth/wrong-password':
      case 'auth/invalid-email':
      case 'auth/user-disabled':
      case 'auth/too-many-requests':
        return AuthException(message, code: code);
      
      case 'network-request-failed':
      case 'unavailable':
        return NetworkException(message, code: code);
      
      case 'permission-denied':
        return PermissionException(message, code: code);
      
      case 'not-found':
        return NotFoundException(message, code: code);
      
      case 'invalid-argument':
      case 'failed-precondition':
        return DataException(message, code: code);
      
      case 'deadline-exceeded':
        return TimeoutException(message, code: code);
      
      default:
        return FirebaseException(message, code: code);
    }
  }
}
