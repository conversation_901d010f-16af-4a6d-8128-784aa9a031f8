import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/utils/extensions.dart';
import '../../core/utils/validators.dart';
import '../../providers/auth_provider.dart';
import '../../providers/event_provider.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_widget.dart';

/// Tela para criação de eventos
class CreateEventScreen extends StatefulWidget {
  const CreateEventScreen({super.key});

  @override
  State<CreateEventScreen> createState() => _CreateEventScreenState();
}

class _CreateEventScreenState extends State<CreateEventScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();

  final List<String> _requiredSkills = [];
  final List<String> _requiredResources = [];
  final TextEditingController _skillController = TextEditingController();
  final TextEditingController _resourceController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _skillController.dispose();
    _resourceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// Constrói a AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('Criar Evento'),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
    );
  }

  /// Constrói o corpo da tela
  Widget _buildBody() {
    return Consumer<EventProvider>(
      builder: (context, eventProvider, child) {
        if (eventProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Criando evento...'),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildEventInfoSection(),
                const SizedBox(height: 24),
                _buildSkillsSection(),
                const SizedBox(height: 24),
                _buildResourcesSection(),
                const SizedBox(height: 32),
                _buildCreateButton(),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Seção de informações do evento
  Widget _buildEventInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informações do Evento',
          style: context.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _nameController,
          label: 'Nome do Evento',
          hint: 'Digite o nome do evento',
          validator: Validators.required,
          prefixIcon: Icons.event,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _descriptionController,
          label: 'Descrição',
          hint: 'Descreva o evento e seus objetivos',
          validator: Validators.required,
          prefixIcon: Icons.description,
          maxLines: 3,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _locationController,
          label: 'Local',
          hint: 'Endereço ou local do evento',
          validator: Validators.required,
          prefixIcon: Icons.location_on,
        ),
      ],
    );
  }

  /// Seção de habilidades necessárias
  Widget _buildSkillsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Habilidades Necessárias',
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Adicione as habilidades que os voluntários devem ter',
          style: context.textTheme.bodyMedium?.copyWith(
            color: AppColors.onBackground.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),
        _buildAddItemField(
          controller: _skillController,
          hint: 'Digite uma habilidade',
          onAdd: _addSkill,
        ),
        const SizedBox(height: 16),
        _buildItemsList(_requiredSkills, _removeSkill),
      ],
    );
  }

  /// Seção de recursos necessários
  Widget _buildResourcesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recursos Necessários',
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Adicione os recursos que os voluntários devem ter',
          style: context.textTheme.bodyMedium?.copyWith(
            color: AppColors.onBackground.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),
        _buildAddItemField(
          controller: _resourceController,
          hint: 'Digite um recurso',
          onAdd: _addResource,
        ),
        const SizedBox(height: 16),
        _buildItemsList(_requiredResources, _removeResource),
      ],
    );
  }

  /// Campo para adicionar item
  Widget _buildAddItemField({
    required TextEditingController controller,
    required String hint,
    required VoidCallback onAdd,
  }) {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: controller,
            decoration: InputDecoration(
              hintText: hint,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onSubmitted: (_) => onAdd(),
          ),
        ),
        const SizedBox(width: 8),
        IconButton(
          onPressed: onAdd,
          icon: const Icon(Icons.add),
          style: IconButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.onPrimary,
          ),
        ),
      ],
    );
  }

  /// Lista de itens
  Widget _buildItemsList(List<String> items, Function(int) onRemove) {
    if (items.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.outline),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          'Nenhum item adicionado',
          style: context.textTheme.bodyMedium?.copyWith(
            color: AppColors.onBackground.withOpacity(0.5),
          ),
        ),
      );
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: items.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        
        return Chip(
          label: Text(item),
          deleteIcon: const Icon(Icons.close, size: 18),
          onDeleted: () => onRemove(index),
          backgroundColor: AppColors.primaryContainer,
          labelStyle: const TextStyle(color: AppColors.onPrimaryContainer),
        );
      }).toList(),
    );
  }

  /// Botão de criar evento
  Widget _buildCreateButton() {
    return Consumer<EventProvider>(
      builder: (context, eventProvider, child) {
        return SizedBox(
          width: double.infinity,
          child: CustomButton(
            text: 'Criar Evento',
            onPressed: eventProvider.isLoading ? null : _handleCreateEvent,
            isLoading: eventProvider.isLoading,
          ),
        );
      },
    );
  }

  /// Adiciona habilidade
  void _addSkill() {
    final skill = _skillController.text.trim();
    if (skill.isNotEmpty && !_requiredSkills.contains(skill)) {
      setState(() {
        _requiredSkills.add(skill);
        _skillController.clear();
      });
    }
  }

  /// Remove habilidade
  void _removeSkill(int index) {
    setState(() {
      _requiredSkills.removeAt(index);
    });
  }

  /// Adiciona recurso
  void _addResource() {
    final resource = _resourceController.text.trim();
    if (resource.isNotEmpty && !_requiredResources.contains(resource)) {
      setState(() {
        _requiredResources.add(resource);
        _resourceController.clear();
      });
    }
  }

  /// Remove recurso
  void _removeResource(int index) {
    setState(() {
      _requiredResources.removeAt(index);
    });
  }

  /// Manipula criação do evento
  Future<void> _handleCreateEvent() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = context.read<AuthProvider>();
    final eventProvider = context.read<EventProvider>();
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      context.showSnackBar('Usuário não autenticado');
      return;
    }

    final success = await eventProvider.createEvent(
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim(),
      location: _locationController.text.trim(),
      createdBy: currentUser.id,
      requiredSkills: _requiredSkills,
      requiredResources: _requiredResources,
    );

    if (success && mounted) {
      context.showSnackBar('Evento criado com sucesso!');
      Navigator.of(context).pop();
    } else if (mounted && eventProvider.errorMessage != null) {
      context.showSnackBar(eventProvider.errorMessage!);
    }
  }
}
