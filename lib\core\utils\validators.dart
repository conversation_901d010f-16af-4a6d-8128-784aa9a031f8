import '../constants/app_strings.dart';

/// Validadores para formulários
class Validators {
  /// Valida se o campo não está vazio
  static String? required(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null 
          ? '$fieldName é ${AppStrings.errorRequired.toLowerCase()}'
          : AppStrings.errorRequired;
    }
    return null;
  }

  /// Valida email
  static String? email(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppStrings.errorRequired;
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return AppStrings.errorInvalidEmail;
    }
    
    return null;
  }

  /// Valida TAG do evento (deve ter pelo menos 3 caracteres alfanuméricos)
  static String? eventTag(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppStrings.errorRequired;
    }
    
    final tag = value.trim().toUpperCase();
    if (tag.length < 3) {
      return 'TAG deve ter pelo menos 3 caracteres';
    }
    
    final tagRegex = RegExp(r'^[A-Z0-9]+$');
    if (!tagRegex.hasMatch(tag)) {
      return 'TAG deve conter apenas letras e números';
    }
    
    return null;
  }

  /// Valida nome (mínimo 2 caracteres)
  static String? name(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppStrings.errorRequired;
    }
    
    if (value.trim().length < 2) {
      return 'Nome deve ter pelo menos 2 caracteres';
    }
    
    return null;
  }

  /// Valida descrição (mínimo 10 caracteres)
  static String? description(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppStrings.errorRequired;
    }
    
    if (value.trim().length < 10) {
      return 'Descrição deve ter pelo menos 10 caracteres';
    }
    
    return null;
  }

  /// Valida duração (deve ser um número positivo)
  static String? duration(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppStrings.errorRequired;
    }
    
    final duration = int.tryParse(value.trim());
    if (duration == null || duration <= 0) {
      return 'Duração deve ser um número positivo';
    }
    
    if (duration > 1440) { // 24 horas em minutos
      return 'Duração não pode exceder 24 horas';
    }
    
    return null;
  }

  /// Valida lista de habilidades/recursos (pelo menos 1 item)
  static String? skillsOrResources(List<String>? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName é obrigatório';
    }
    
    if (value.any((item) => item.trim().isEmpty)) {
      return '$fieldName não pode conter itens vazios';
    }
    
    return null;
  }

  /// Valida horário (formato HH:mm)
  static String? time(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppStrings.errorRequired;
    }
    
    final timeRegex = RegExp(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$');
    if (!timeRegex.hasMatch(value.trim())) {
      return 'Formato de horário inválido (HH:mm)';
    }
    
    return null;
  }

  /// Valida se a data não é no passado
  static String? futureDate(DateTime? value) {
    if (value == null) {
      return AppStrings.errorRequired;
    }
    
    if (value.isBefore(DateTime.now())) {
      return 'Data não pode ser no passado';
    }
    
    return null;
  }

  /// Combina múltiplos validadores
  static String? combine(String? value, List<String? Function(String?)> validators) {
    for (final validator in validators) {
      final result = validator(value);
      if (result != null) return result;
    }
    return null;
  }

  /// Valida se dois campos são iguais (ex: confirmação de senha)
  static String? Function(String?) match(String? originalValue, String fieldName) {
    return (String? value) {
      if (value != originalValue) {
        return '$fieldName não confere';
      }
      return null;
    };
  }

  /// Valida comprimento mínimo
  static String? Function(String?) minLength(int min, String fieldName) {
    return (String? value) {
      if (value == null || value.length < min) {
        return '$fieldName deve ter pelo menos $min caracteres';
      }
      return null;
    };
  }

  /// Valida comprimento máximo
  static String? Function(String?) maxLength(int max, String fieldName) {
    return (String? value) {
      if (value != null && value.length > max) {
        return '$fieldName deve ter no máximo $max caracteres';
      }
      return null;
    };
  }
}
