import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import '../errors/exceptions.dart';

/// Serviço de notificações
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  String? _fcmToken;

  /// Token FCM atual
  String? get fcmToken => _fcmToken;

  /// Inicializa o serviço de notificações
  Future<void> initialize() async {
    try {
      // Solicitar permissão para notificações
      final settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        debugPrint('Permissão para notificações concedida');
      } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
        debugPrint('Permissão provisória para notificações concedida');
      } else {
        debugPrint('Permissão para notificações negada');
        return;
      }

      // Obter token FCM
      _fcmToken = await _messaging.getToken();
      debugPrint('FCM Token: $_fcmToken');

      // Configurar handlers de mensagens
      _setupMessageHandlers();

      // Configurar refresh do token
      _messaging.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        debugPrint('FCM Token atualizado: $newToken');
        // Aqui você pode salvar o novo token no Firestore se necessário
      });

    } catch (e) {
      throw DataException('Erro ao inicializar notificações: ${e.toString()}');
    }
  }

  /// Configura os handlers de mensagens
  void _setupMessageHandlers() {
    // Handler para mensagens em foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Mensagem recebida em foreground: ${message.notification?.title}');
      _handleMessage(message);
    });

    // Handler para quando o app é aberto através de uma notificação
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('App aberto através de notificação: ${message.notification?.title}');
      _handleMessage(message);
    });

    // Handler para mensagens em background
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  /// Processa mensagens recebidas
  void _handleMessage(RemoteMessage message) {
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      debugPrint('Título: ${notification.title}');
      debugPrint('Corpo: ${notification.body}');
    }

    if (data.isNotEmpty) {
      debugPrint('Dados: $data');
      
      // Processar diferentes tipos de notificação
      final type = data['type'];
      switch (type) {
        case 'task_assigned':
          _handleTaskAssigned(data);
          break;
        case 'task_completed':
          _handleTaskCompleted(data);
          break;
        case 'event_updated':
          _handleEventUpdated(data);
          break;
        case 'volunteer_joined':
          _handleVolunteerJoined(data);
          break;
        default:
          debugPrint('Tipo de notificação desconhecido: $type');
      }
    }
  }

  /// Handler para tarefa atribuída
  void _handleTaskAssigned(Map<String, dynamic> data) {
    final taskId = data['taskId'];
    final eventId = data['eventId'];
    debugPrint('Tarefa $taskId atribuída no evento $eventId');
    // Implementar navegação ou ação específica
  }

  /// Handler para tarefa concluída
  void _handleTaskCompleted(Map<String, dynamic> data) {
    final taskId = data['taskId'];
    final eventId = data['eventId'];
    debugPrint('Tarefa $taskId concluída no evento $eventId');
    // Implementar ação específica
  }

  /// Handler para evento atualizado
  void _handleEventUpdated(Map<String, dynamic> data) {
    final eventId = data['eventId'];
    debugPrint('Evento $eventId foi atualizado');
    // Implementar ação específica
  }

  /// Handler para voluntário que entrou
  void _handleVolunteerJoined(Map<String, dynamic> data) {
    final eventId = data['eventId'];
    final userId = data['userId'];
    debugPrint('Voluntário $userId entrou no evento $eventId');
    // Implementar ação específica
  }

  /// Subscreve a um tópico
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _messaging.subscribeToTopic(topic);
      debugPrint('Subscrito ao tópico: $topic');
    } catch (e) {
      throw DataException('Erro ao subscrever ao tópico: ${e.toString()}');
    }
  }

  /// Remove subscrição de um tópico
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _messaging.unsubscribeFromTopic(topic);
      debugPrint('Subscrição removida do tópico: $topic');
    } catch (e) {
      throw DataException('Erro ao remover subscrição: ${e.toString()}');
    }
  }

  /// Subscreve a notificações de um evento
  Future<void> subscribeToEvent(String eventId) async {
    await subscribeToTopic('event_$eventId');
  }

  /// Remove subscrição de notificações de um evento
  Future<void> unsubscribeFromEvent(String eventId) async {
    await unsubscribeFromTopic('event_$eventId');
  }

  /// Verifica se as notificações estão habilitadas
  Future<bool> areNotificationsEnabled() async {
    try {
      final settings = await _messaging.getNotificationSettings();
      return settings.authorizationStatus == AuthorizationStatus.authorized ||
             settings.authorizationStatus == AuthorizationStatus.provisional;
    } catch (e) {
      return false;
    }
  }

  /// Solicita permissão para notificações novamente
  Future<bool> requestPermission() async {
    try {
      final settings = await _messaging.requestPermission();
      return settings.authorizationStatus == AuthorizationStatus.authorized ||
             settings.authorizationStatus == AuthorizationStatus.provisional;
    } catch (e) {
      return false;
    }
  }

  /// Limpa todas as notificações
  Future<void> clearAllNotifications() async {
    // Esta funcionalidade depende de plugins específicos da plataforma
    // Por enquanto, apenas log
    debugPrint('Limpando todas as notificações');
  }

  /// Obtém informações sobre as configurações de notificação
  Future<NotificationSettings> getNotificationSettings() async {
    return await _messaging.getNotificationSettings();
  }
}

/// Handler para mensagens em background (deve ser função top-level)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  debugPrint('Mensagem em background: ${message.notification?.title}');
  
  // Processar a mensagem em background
  final data = message.data;
  if (data.isNotEmpty) {
    debugPrint('Dados em background: $data');
    // Implementar lógica específica para background se necessário
  }
}

/// Tipos de notificação
enum NotificationType {
  taskAssigned('task_assigned'),
  taskCompleted('task_completed'),
  eventUpdated('event_updated'),
  volunteerJoined('volunteer_joined');

  const NotificationType(this.value);
  final String value;

  static NotificationType fromString(String value) {
    return NotificationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => NotificationType.taskAssigned,
    );
  }
}

/// Dados para envio de notificação
class NotificationData {
  final String title;
  final String body;
  final NotificationType type;
  final Map<String, String> data;

  const NotificationData({
    required this.title,
    required this.body,
    required this.type,
    required this.data,
  });

  Map<String, dynamic> toMap() {
    return {
      'notification': {
        'title': title,
        'body': body,
      },
      'data': {
        'type': type.value,
        ...data,
      },
    };
  }
}
